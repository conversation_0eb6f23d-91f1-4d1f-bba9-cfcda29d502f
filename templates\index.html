<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI文章撰写工具</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .news-card {
            transition: transform 0.2s;
            cursor: pointer;
        }
        .news-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .topic-tag {
            display: inline-block;
            margin: 2px;
            padding: 4px 8px;
            background-color: #e9ecef;
            border-radius: 12px;
            font-size: 0.8em;
        }
        .loading {
            display: none;
        }
        .article-preview {
            max-height: 300px;
            overflow-y: auto;
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="#">
                <i class="bi bi-newspaper"></i> AI文章撰写工具
            </a>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- 热点话题 -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="bi bi-fire"></i> 热点话题</h5>
                    </div>
                    <div class="card-body">
                        <div id="trending-topics">
                            <div class="text-center">
                                <div class="spinner-border" role="status">
                                    <span class="visually-hidden">加载中...</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- 新闻列表 -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0"><i class="bi bi-list-ul"></i> 热点新闻</h5>
                        <button class="btn btn-sm btn-outline-primary" onclick="refreshNews()">
                            <i class="bi bi-arrow-clockwise"></i> 刷新
                        </button>
                    </div>
                    <div class="card-body" style="max-height: 600px; overflow-y: auto;">
                        <div id="news-list">
                            <div class="text-center">
                                <div class="spinner-border" role="status">
                                    <span class="visually-hidden">加载中...</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 文章撰写 -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="bi bi-pencil-square"></i> 文章撰写</h5>
                    </div>
                    <div class="card-body">
                        <div id="article-form" style="display: none;">
                            <div class="mb-3">
                                <label class="form-label">选中新闻：</label>
                                <div id="selected-news" class="alert alert-info"></div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="article-type" class="form-label">文章类型：</label>
                                <select class="form-select" id="article-type">
                                    <option value="breaking_news">突发新闻</option>
                                    <option value="analysis">深度分析</option>
                                    <option value="feature">特稿报道</option>
                                </select>
                            </div>
                            
                            <div class="mb-3">
                                <label for="writing-style" class="form-label">写作风格：</label>
                                <select class="form-select" id="writing-style">
                                    <option value="professional">专业严谨</option>
                                    <option value="casual">轻松易读</option>
                                    <option value="academic">学术分析</option>
                                </select>
                            </div>
                            
                            <button class="btn btn-primary" onclick="writeArticle()">
                                <i class="bi bi-magic"></i> 开始撰写
                            </button>
                        </div>
                        
                        <div id="no-selection" class="text-muted text-center">
                            <i class="bi bi-arrow-left"></i> 请先选择一条新闻
                        </div>
                        
                        <div id="article-loading" class="loading text-center">
                            <div class="spinner-border" role="status">
                                <span class="visually-hidden">正在撰写文章...</span>
                            </div>
                            <p class="mt-2">AI正在撰写文章，请稍候...</p>
                        </div>
                        
                        <div id="article-result" style="display: none;">
                            <h6>生成的文章：</h6>
                            <div id="article-content" class="article-preview"></div>
                            <div class="mt-3">
                                <button class="btn btn-success btn-sm" onclick="downloadArticle()">
                                    <i class="bi bi-download"></i> 下载文章
                                </button>
                                <button class="btn btn-secondary btn-sm" onclick="copyArticle()">
                                    <i class="bi bi-clipboard"></i> 复制内容
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 已生成文章列表 -->
                <div class="card mt-3">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="bi bi-folder"></i> 已生成文章</h6>
                    </div>
                    <div class="card-body" style="max-height: 300px; overflow-y: auto;">
                        <div id="articles-list">
                            <div class="text-center">
                                <div class="spinner-border spinner-border-sm" role="status">
                                    <span class="visually-hidden">加载中...</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let selectedNewsId = null;
        let currentArticleFilename = null;

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadTrendingTopics();
            loadNews();
            loadArticlesList();
        });

        // 加载热点话题
        function loadTrendingTopics() {
            fetch('/api/topics')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        displayTrendingTopics(data.data);
                    } else {
                        document.getElementById('trending-topics').innerHTML = 
                            '<div class="text-danger">加载失败: ' + data.error + '</div>';
                    }
                })
                .catch(error => {
                    document.getElementById('trending-topics').innerHTML = 
                        '<div class="text-danger">网络错误: ' + error.message + '</div>';
                });
        }

        // 显示热点话题
        function displayTrendingTopics(topics) {
            const container = document.getElementById('trending-topics');
            if (topics.length === 0) {
                container.innerHTML = '<div class="text-muted">暂无热点话题</div>';
                return;
            }

            const html = topics.map(topic => 
                `<span class="topic-tag">${topic.word} (${topic.count})</span>`
            ).join('');
            
            container.innerHTML = html;
        }

        // 加载新闻列表
        function loadNews() {
            fetch('/api/news?limit=20')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        displayNews(data.data);
                    } else {
                        document.getElementById('news-list').innerHTML = 
                            '<div class="text-danger">加载失败: ' + data.error + '</div>';
                    }
                })
                .catch(error => {
                    document.getElementById('news-list').innerHTML = 
                        '<div class="text-danger">网络错误: ' + error.message + '</div>';
                });
        }

        // 显示新闻列表
        function displayNews(newsList) {
            const container = document.getElementById('news-list');
            if (newsList.length === 0) {
                container.innerHTML = '<div class="text-muted">暂无新闻</div>';
                return;
            }

            const html = newsList.map(news => `
                <div class="news-card card mb-2" onclick="selectNews(${news.id}, '${news.title.replace(/'/g, "\\'")}')">
                    <div class="card-body p-3">
                        <h6 class="card-title">${news.title}</h6>
                        <p class="card-text text-muted small">${news.summary.substring(0, 100)}...</p>
                        <div class="d-flex justify-content-between align-items-center">
                            <small class="text-muted">${news.source}</small>
                            <small class="text-muted">${new Date(news.publish_time).toLocaleString()}</small>
                        </div>
                    </div>
                </div>
            `).join('');
            
            container.innerHTML = html;
        }

        // 选择新闻
        function selectNews(newsId, title) {
            selectedNewsId = newsId;
            
            // 更新选中状态
            document.querySelectorAll('.news-card').forEach(card => {
                card.classList.remove('border-primary');
            });
            event.currentTarget.classList.add('border-primary');
            
            // 显示选中的新闻
            document.getElementById('selected-news').textContent = title;
            document.getElementById('article-form').style.display = 'block';
            document.getElementById('no-selection').style.display = 'none';
        }

        // 撰写文章
        function writeArticle() {
            if (!selectedNewsId) {
                alert('请先选择一条新闻');
                return;
            }

            const articleType = document.getElementById('article-type').value;
            const writingStyle = document.getElementById('writing-style').value;

            // 显示加载状态
            document.getElementById('article-loading').style.display = 'block';
            document.getElementById('article-result').style.display = 'none';

            fetch('/api/write_article', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    news_id: selectedNewsId,
                    article_type: articleType,
                    style: writingStyle
                })
            })
            .then(response => response.json())
            .then(data => {
                document.getElementById('article-loading').style.display = 'none';
                
                if (data.success) {
                    document.getElementById('article-content').textContent = data.data.article;
                    document.getElementById('article-result').style.display = 'block';
                    currentArticleFilename = data.data.filename;
                    
                    // 刷新文章列表
                    loadArticlesList();
                } else {
                    alert('文章撰写失败: ' + data.error);
                }
            })
            .catch(error => {
                document.getElementById('article-loading').style.display = 'none';
                alert('网络错误: ' + error.message);
            });
        }

        // 下载文章
        function downloadArticle() {
            if (currentArticleFilename) {
                const filename = currentArticleFilename.split('/').pop();
                window.open('/download/' + filename, '_blank');
            }
        }

        // 复制文章内容
        function copyArticle() {
            const content = document.getElementById('article-content').textContent;
            navigator.clipboard.writeText(content).then(() => {
                alert('文章内容已复制到剪贴板');
            });
        }

        // 刷新新闻
        function refreshNews() {
            loadTrendingTopics();
            loadNews();
        }

        // 加载已生成文章列表
        function loadArticlesList() {
            fetch('/api/articles')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        displayArticlesList(data.data);
                    } else {
                        document.getElementById('articles-list').innerHTML = 
                            '<div class="text-danger">加载失败: ' + data.error + '</div>';
                    }
                })
                .catch(error => {
                    document.getElementById('articles-list').innerHTML = 
                        '<div class="text-danger">网络错误: ' + error.message + '</div>';
                });
        }

        // 显示文章列表
        function displayArticlesList(articles) {
            const container = document.getElementById('articles-list');
            if (articles.length === 0) {
                container.innerHTML = '<div class="text-muted">暂无已生成文章</div>';
                return;
            }

            const html = articles.map(article => `
                <div class="d-flex justify-content-between align-items-center py-2 border-bottom">
                    <div>
                        <div class="fw-bold">${article.title}</div>
                        <small class="text-muted">${new Date(article.created_time).toLocaleString()}</small>
                    </div>
                    <a href="/download/${article.filename}" class="btn btn-sm btn-outline-primary">
                        <i class="bi bi-download"></i>
                    </a>
                </div>
            `).join('');
            
            container.innerHTML = html;
        }
    </script>
</body>
</html>
