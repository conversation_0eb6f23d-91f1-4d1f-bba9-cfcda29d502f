#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Web界面模块
提供用户友好的Web界面
"""

from flask import Flask, render_template, request, jsonify, send_file
import os
import json
from datetime import datetime
from .news_analyzer import NewsAnalyzer
from .article_writer import ArticleWriter

def create_app():
    """创建Flask应用"""
    app = Flask(__name__)
    app.secret_key = os.getenv('SECRET_KEY', 'your-secret-key-here')
    
    # 初始化组件
    news_analyzer = NewsAnalyzer()
    article_writer = ArticleWriter()
    
    @app.route('/')
    def index():
        """主页"""
        return render_template('index.html')
    
    @app.route('/api/news')
    def get_news():
        """获取热点新闻API"""
        try:
            limit = request.args.get('limit', 20, type=int)
            news_list = news_analyzer.get_trending_news(limit)
            
            # 转换datetime为字符串
            for news in news_list:
                news['publish_time'] = news['publish_time'].isoformat()
            
            return jsonify({
                'success': True,
                'data': news_list,
                'count': len(news_list)
            })
        except Exception as e:
            return jsonify({
                'success': False,
                'error': str(e)
            }), 500
    
    @app.route('/api/topics')
    def get_trending_topics():
        """获取热点话题API"""
        try:
            news_list = news_analyzer.get_trending_news(50)
            topics = news_analyzer.analyze_trending_topics(news_list)
            
            return jsonify({
                'success': True,
                'data': [{'word': word, 'count': count} for word, count in topics]
            })
        except Exception as e:
            return jsonify({
                'success': False,
                'error': str(e)
            }), 500
    
    @app.route('/api/write_article', methods=['POST'])
    def write_article():
        """撰写文章API"""
        try:
            data = request.get_json()
            news_id = data.get('news_id')
            article_type = data.get('article_type', 'breaking_news')
            style = data.get('style', 'professional')
            
            # 获取新闻数据
            news_list = news_analyzer.get_trending_news(100)
            selected_news = None
            
            for news in news_list:
                if news['id'] == news_id:
                    selected_news = news
                    break
            
            if not selected_news:
                return jsonify({
                    'success': False,
                    'error': '未找到指定新闻'
                }), 404
            
            # 撰写文章
            article = article_writer.write_article(selected_news, article_type, style)
            
            # 保存文章
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"articles/article_{news_id}_{timestamp}.md"
            os.makedirs("articles", exist_ok=True)
            
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(article)
            
            return jsonify({
                'success': True,
                'data': {
                    'article': article,
                    'filename': filename
                }
            })
            
        except Exception as e:
            return jsonify({
                'success': False,
                'error': str(e)
            }), 500
    
    @app.route('/api/articles')
    def list_articles():
        """列出已生成的文章"""
        try:
            articles_dir = "articles"
            if not os.path.exists(articles_dir):
                return jsonify({
                    'success': True,
                    'data': []
                })
            
            articles = []
            for filename in os.listdir(articles_dir):
                if filename.endswith('.md'):
                    filepath = os.path.join(articles_dir, filename)
                    stat = os.stat(filepath)
                    
                    # 读取文章标题
                    with open(filepath, 'r', encoding='utf-8') as f:
                        first_line = f.readline().strip()
                        title = first_line.replace('# ', '') if first_line.startswith('# ') else filename
                    
                    articles.append({
                        'filename': filename,
                        'title': title,
                        'size': stat.st_size,
                        'created_time': datetime.fromtimestamp(stat.st_ctime).isoformat(),
                        'modified_time': datetime.fromtimestamp(stat.st_mtime).isoformat()
                    })
            
            # 按修改时间排序
            articles.sort(key=lambda x: x['modified_time'], reverse=True)
            
            return jsonify({
                'success': True,
                'data': articles
            })
            
        except Exception as e:
            return jsonify({
                'success': False,
                'error': str(e)
            }), 500
    
    @app.route('/api/articles/<filename>')
    def get_article(filename):
        """获取文章内容"""
        try:
            filepath = os.path.join("articles", filename)
            if not os.path.exists(filepath):
                return jsonify({
                    'success': False,
                    'error': '文章不存在'
                }), 404
            
            with open(filepath, 'r', encoding='utf-8') as f:
                content = f.read()
            
            return jsonify({
                'success': True,
                'data': {
                    'filename': filename,
                    'content': content
                }
            })
            
        except Exception as e:
            return jsonify({
                'success': False,
                'error': str(e)
            }), 500
    
    @app.route('/download/<filename>')
    def download_article(filename):
        """下载文章"""
        try:
            filepath = os.path.join("articles", filename)
            if not os.path.exists(filepath):
                return "文件不存在", 404
            
            return send_file(filepath, as_attachment=True)
            
        except Exception as e:
            return f"下载失败: {e}", 500
    
    return app
