#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
新闻分析器模块
负责获取和分析实时热点新闻
"""

import requests
import feedparser
import jieba
from collections import Counter
from datetime import datetime, timedelta
from newspaper import Article
import json
import time

class NewsAnalyzer:
    """新闻分析器类"""
    
    def __init__(self):
        """初始化新闻分析器"""
        self.news_sources = [
            {
                'name': '新浪新闻',
                'rss_url': 'http://rss.sina.com.cn/news/china/focus15.xml',
                'type': 'rss'
            },
            {
                'name': '网易新闻',
                'rss_url': 'http://news.163.com/special/00011K6L/rss_newstop.xml',
                'type': 'rss'
            },
            {
                'name': '腾讯新闻',
                'api_url': 'https://news.qq.com/ninja/channel_preview_ftx_cp/list.shtml',
                'type': 'api'
            }
        ]
        
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
    
    def get_trending_news(self, limit=20):
        """获取热点新闻"""
        all_news = []
        
        for source in self.news_sources:
            try:
                if source['type'] == 'rss':
                    news_items = self._parse_rss_feed(source['rss_url'], source['name'])
                elif source['type'] == 'api':
                    news_items = self._parse_api_feed(source['api_url'], source['name'])
                
                all_news.extend(news_items)
                time.sleep(1)  # 避免请求过于频繁
                
            except Exception as e:
                print(f"获取 {source['name']} 新闻失败: {e}")
                continue
        
        # 按时间排序并去重
        unique_news = self._deduplicate_news(all_news)
        trending_news = sorted(unique_news, key=lambda x: x['publish_time'], reverse=True)
        
        return trending_news[:limit]
    
    def _parse_rss_feed(self, rss_url, source_name):
        """解析RSS源"""
        try:
            feed = feedparser.parse(rss_url)
            news_items = []
            
            for entry in feed.entries:
                news_item = {
                    'id': hash(entry.title + entry.link),
                    'title': entry.title,
                    'link': entry.link,
                    'summary': getattr(entry, 'summary', ''),
                    'publish_time': datetime(*entry.published_parsed[:6]) if hasattr(entry, 'published_parsed') else datetime.now(),
                    'source': source_name,
                    'content': ''
                }
                
                # 获取完整内容
                try:
                    article = Article(entry.link, language='zh')
                    article.download()
                    article.parse()
                    news_item['content'] = article.text
                except:
                    news_item['content'] = news_item['summary']
                
                news_items.append(news_item)
            
            return news_items
            
        except Exception as e:
            print(f"解析RSS源失败: {e}")
            return []
    
    def _parse_api_feed(self, api_url, source_name):
        """解析API源（示例实现）"""
        # 这里是示例实现，实际需要根据具体API调整
        try:
            response = requests.get(api_url, headers=self.headers, timeout=10)
            # 根据实际API格式解析数据
            return []
        except Exception as e:
            print(f"解析API源失败: {e}")
            return []
    
    def _deduplicate_news(self, news_list):
        """新闻去重"""
        seen_titles = set()
        unique_news = []
        
        for news in news_list:
            # 简单的标题相似度去重
            title_words = set(jieba.cut(news['title']))
            is_duplicate = False
            
            for seen_title in seen_titles:
                seen_words = set(jieba.cut(seen_title))
                similarity = len(title_words & seen_words) / len(title_words | seen_words)
                if similarity > 0.7:  # 相似度阈值
                    is_duplicate = True
                    break
            
            if not is_duplicate:
                seen_titles.add(news['title'])
                unique_news.append(news)
        
        return unique_news
    
    def analyze_trending_topics(self, news_list):
        """分析热点话题"""
        all_words = []
        
        for news in news_list:
            # 分词并过滤停用词
            words = jieba.cut(news['title'] + ' ' + news['summary'])
            filtered_words = [word for word in words if len(word) > 1 and word.isalnum()]
            all_words.extend(filtered_words)
        
        # 统计词频
        word_freq = Counter(all_words)
        trending_topics = word_freq.most_common(10)
        
        return trending_topics
    
    def get_news_sentiment(self, news_content):
        """分析新闻情感倾向（简单实现）"""
        positive_words = ['好', '优秀', '成功', '增长', '提升', '改善', '突破']
        negative_words = ['坏', '失败', '下降', '问题', '危机', '困难', '风险']
        
        words = jieba.cut(news_content)
        positive_count = sum(1 for word in words if word in positive_words)
        negative_count = sum(1 for word in words if word in negative_words)
        
        if positive_count > negative_count:
            return 'positive'
        elif negative_count > positive_count:
            return 'negative'
        else:
            return 'neutral'
