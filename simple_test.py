#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试脚本
"""

def test_imports():
    """测试导入"""
    print("测试导入...")
    
    try:
        # 测试标准库
        import os, sys, json, re
        from datetime import datetime
        print("标准库导入成功")
        
        # 测试项目模块
        from src.news_analyzer import NewsAnalyzer
        from src.article_writer import ArticleWriter
        from src.web_interface import create_app
        print("项目模块导入成功")
        
        return True
    except Exception as e:
        print(f"导入失败: {e}")
        return False

def test_functionality():
    """测试功能"""
    print("测试基本功能...")
    
    try:
        from src.news_analyzer import NewsAnalyzer
        from src.article_writer import ArticleWriter
        from datetime import datetime
        
        # 创建实例
        analyzer = NewsAnalyzer()
        writer = ArticleWriter()
        print("实例创建成功")
        
        # 测试文章生成
        test_news = {
            'id': 12345,
            'title': '测试新闻标题',
            'content': '这是测试内容',
            'summary': '测试摘要',
            'source': '测试来源',
            'link': 'http://test.com',
            'publish_time': datetime.now()
        }
        
        article = writer.write_article(test_news)
        print(f"文章生成成功，长度: {len(article)}")
        
        return True
    except Exception as e:
        print(f"功能测试失败: {e}")
        return False

if __name__ == "__main__":
    print("=== 简单测试开始 ===")
    
    if test_imports():
        if test_functionality():
            print("所有测试通过!")
        else:
            print("功能测试失败")
    else:
        print("导入测试失败")