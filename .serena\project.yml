name: aigc-wenzhang
language: python
description: AI文章撰写工具 - 分析实时热点新闻并撰写高质量独家首发报道

# Python项目配置
python:
  version: "3.8+"
  main_module: main.py
  requirements_file: requirements.txt
  virtual_env: venv

# 项目结构
directories:
  source: src
  templates: templates
  output: articles
  
# 忽略文件
ignore_patterns:
  - "*.pyc"
  - "__pycache__/"
  - "venv/"
  - ".env"
  - "articles/"
  - "*.log"

# 开发工具配置
tools:
  linter: flake8
  formatter: black
  test_runner: pytest

# 项目特定配置
features:
  - web_interface
  - news_analysis
  - ai_writing
  - article_management
