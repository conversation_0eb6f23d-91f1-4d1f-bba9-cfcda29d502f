# AI文章撰写工具

一个基于AI的智能文章撰写工具，能够分析当前实时热点新闻，自动撰写高质量的独家首发报道。

## 功能特点

- 🔥 **实时热点分析**: 自动获取多个新闻源的热点新闻
- 📝 **智能文章撰写**: 基于AI技术生成高质量原创文章
- 🎯 **多种文章类型**: 支持突发新闻、深度分析、特稿报道等
- 🌐 **Web界面**: 提供用户友好的Web操作界面
- 📊 **热点话题分析**: 自动分析和展示当前热点话题
- 💾 **文章管理**: 自动保存和管理生成的文章

## 技术架构

- **后端**: Python + Flask
- **前端**: HTML + Bootstrap + JavaScript
- **AI引擎**: OpenAI GPT (可选)
- **新闻获取**: RSS订阅 + 网页爬虫
- **文本处理**: jieba分词 + newspaper3k

## 安装和使用

### 1. 环境准备

```bash
# 克隆项目
git clone <repository-url>
cd aigc-wenzhang

# 创建虚拟环境
python -m venv venv

# 激活虚拟环境
# Windows:
venv\Scripts\activate
# Linux/Mac:
source venv/bin/activate

# 安装依赖
pip install -r requirements.txt
```

### 2. 配置环境变量

```bash
# 复制环境变量模板
cp .env.example .env

# 编辑.env文件，配置必要的API密钥
# 特别是OPENAI_API_KEY（如果要使用AI生成功能）
```

### 3. 运行程序

#### Web界面模式（推荐）

```bash
python main.py
# 选择 1 启动Web界面
# 然后在浏览器中访问 http://localhost:5000
```

#### 命令行模式

```bash
python main.py
# 选择 2 进入命令行模式
```

## 使用说明

### Web界面使用

1. 打开浏览器访问 `http://localhost:5000`
2. 查看热点话题和新闻列表
3. 点击选择要撰写文章的新闻
4. 选择文章类型和写作风格
5. 点击"开始撰写"生成文章
6. 下载或复制生成的文章

### 命令行使用

1. 运行程序后选择命令行模式
2. 程序会自动获取热点新闻
3. 选择要撰写的新闻编号
4. 等待AI生成文章
5. 文章会自动保存到articles目录

## 项目结构

```
aigc-wenzhang/
├── main.py                 # 主程序入口
├── requirements.txt        # 依赖包列表
├── .env.example           # 环境变量模板
├── README.md              # 项目说明
├── src/                   # 核心模块
│   ├── __init__.py
│   ├── news_analyzer.py   # 新闻分析器
│   ├── article_writer.py  # 文章撰写器
│   └── web_interface.py   # Web界面
├── templates/             # HTML模板
│   └── index.html
└── articles/              # 生成的文章存储目录
```

## 配置说明

### 环境变量

- `OPENAI_API_KEY`: OpenAI API密钥（可选，用于AI生成）
- `SECRET_KEY`: Flask应用密钥
- `NEWS_API_KEY`: 新闻API密钥（可选）

### 新闻源配置

程序默认配置了以下新闻源：
- 新浪新闻RSS
- 网易新闻RSS
- 腾讯新闻API（需要配置）

可以在 `src/news_analyzer.py` 中修改和添加更多新闻源。

## 注意事项

1. **API限制**: 如果使用OpenAI API，请注意API调用限制和费用
2. **新闻源**: 部分新闻源可能需要特殊配置或API密钥
3. **内容质量**: AI生成的内容仅供参考，建议人工审核后发布
4. **版权问题**: 请遵守相关法律法规，尊重原创内容版权

## 扩展功能

- [ ] 支持更多新闻源
- [ ] 添加文章质量评估
- [ ] 支持多语言文章生成
- [ ] 添加用户管理系统
- [ ] 集成社交媒体发布
- [ ] 添加SEO优化功能

## 贡献

欢迎提交Issue和Pull Request来改进这个项目。

## 许可证

MIT License
