#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文章撰写器模块
负责基于新闻内容撰写高质量文章
"""

import os
import openai
from datetime import datetime
import json
import re

class ArticleWriter:
    """文章撰写器类"""
    
    def __init__(self):
        """初始化文章撰写器"""
        self.openai_api_key = os.getenv('OPENAI_API_KEY')
        if self.openai_api_key:
            openai.api_key = self.openai_api_key
        
        # 文章模板
        self.article_templates = {
            'breaking_news': {
                'title_format': '【独家】{topic}：{key_point}',
                'structure': ['导语', '事件详情', '背景分析', '影响评估', '专家观点', '结语']
            },
            'analysis': {
                'title_format': '深度解析：{topic}背后的{angle}',
                'structure': ['引言', '现状分析', '原因探讨', '趋势预测', '建议对策', '总结']
            },
            'feature': {
                'title_format': '{topic}全景：{subtitle}',
                'structure': ['开篇', '核心内容', '多角度分析', '案例展示', '未来展望', '结尾']
            }
        }
    
    def write_article(self, news_data, article_type='breaking_news', style='professional'):
        """撰写文章"""
        try:
            # 分析新闻内容
            analysis = self._analyze_news_content(news_data)
            
            # 生成文章标题
            title = self._generate_title(news_data, analysis, article_type)
            
            # 生成文章内容
            content = self._generate_content(news_data, analysis, article_type, style)
            
            # 组装完整文章
            article = self._format_article(title, content, news_data)
            
            return article
            
        except Exception as e:
            print(f"文章撰写失败: {e}")
            return self._generate_fallback_article(news_data)
    
    def _analyze_news_content(self, news_data):
        """分析新闻内容"""
        analysis = {
            'key_points': self._extract_key_points(news_data['content']),
            'entities': self._extract_entities(news_data['content']),
            'sentiment': self._analyze_sentiment(news_data['content']),
            'category': self._categorize_news(news_data['title']),
            'urgency': self._assess_urgency(news_data)
        }
        return analysis
    
    def _generate_title(self, news_data, analysis, article_type):
        """生成文章标题"""
        if self.openai_api_key:
            return self._generate_ai_title(news_data, analysis, article_type)
        else:
            return self._generate_template_title(news_data, analysis, article_type)
    
    def _generate_ai_title(self, news_data, analysis, article_type):
        """使用AI生成标题"""
        prompt = f"""
        基于以下新闻信息，生成一个吸引人的{article_type}类型文章标题：
        
        原标题：{news_data['title']}
        关键点：{', '.join(analysis['key_points'][:3])}
        类别：{analysis['category']}
        情感：{analysis['sentiment']}
        
        要求：
        1. 标题要有新闻价值和吸引力
        2. 体现独家或首发特色
        3. 长度控制在15-25字
        4. 避免夸大或误导
        
        请只返回标题，不要其他内容。
        """
        
        try:
            response = openai.ChatCompletion.create(
                model="gpt-3.5-turbo",
                messages=[{"role": "user", "content": prompt}],
                max_tokens=100,
                temperature=0.7
            )
            return response.choices[0].message.content.strip()
        except:
            return self._generate_template_title(news_data, analysis, article_type)
    
    def _generate_template_title(self, news_data, analysis, article_type):
        """使用模板生成标题"""
        template = self.article_templates[article_type]['title_format']
        
        # 提取主题和关键点
        topic = analysis['entities'][0] if analysis['entities'] else '重要事件'
        key_point = analysis['key_points'][0] if analysis['key_points'] else '最新进展'
        
        return template.format(topic=topic, key_point=key_point, angle='深层原因')
    
    def _generate_content(self, news_data, analysis, article_type, style):
        """生成文章内容"""
        if self.openai_api_key:
            return self._generate_ai_content(news_data, analysis, article_type, style)
        else:
            return self._generate_template_content(news_data, analysis, article_type)
    
    def _generate_ai_content(self, news_data, analysis, article_type, style):
        """使用AI生成内容"""
        structure = self.article_templates[article_type]['structure']
        
        prompt = f"""
        基于以下新闻信息，撰写一篇{style}风格的{article_type}类型文章：
        
        原新闻：
        标题：{news_data['title']}
        内容：{news_data['content'][:1000]}
        来源：{news_data['source']}
        
        分析结果：
        关键点：{', '.join(analysis['key_points'])}
        实体：{', '.join(analysis['entities'])}
        类别：{analysis['category']}
        
        文章结构：{' -> '.join(structure)}
        
        要求：
        1. 文章长度800-1200字
        2. 语言流畅，逻辑清晰
        3. 体现独家分析和深度思考
        4. 避免抄袭原文，要有原创观点
        5. 包含数据支撑和专业分析
        6. 结构完整，每个部分都要充实
        
        请按照指定结构撰写完整文章。
        """
        
        try:
            response = openai.ChatCompletion.create(
                model="gpt-3.5-turbo",
                messages=[{"role": "user", "content": prompt}],
                max_tokens=2000,
                temperature=0.8
            )
            return response.choices[0].message.content.strip()
        except Exception as e:
            print(f"AI生成内容失败: {e}")
            return self._generate_template_content(news_data, analysis, article_type)
    
    def _generate_template_content(self, news_data, analysis, article_type):
        """使用模板生成内容"""
        structure = self.article_templates[article_type]['structure']
        content_parts = []
        
        for section in structure:
            if section == '导语' or section == '引言' or section == '开篇':
                part = f"**{section}**\n\n据{news_data['source']}报道，{news_data['title']}。这一事件引发了广泛关注，本文将对此进行深入分析。\n"
            elif section == '事件详情' or section == '现状分析' or section == '核心内容':
                part = f"**{section}**\n\n{news_data['content'][:200]}...\n\n从目前掌握的信息来看，此事件具有以下特点：\n"
                for i, point in enumerate(analysis['key_points'][:3], 1):
                    part += f"{i}. {point}\n"
                part += "\n"
            elif section == '背景分析' or section == '原因探讨':
                part = f"**{section}**\n\n要理解这一事件的深层含义，需要从多个角度进行分析。相关专家指出，这一现象的出现并非偶然，而是多种因素共同作用的结果。\n"
            else:
                part = f"**{section}**\n\n综合以上分析，我们可以看出这一事件的重要意义。未来发展值得持续关注。\n"
            
            content_parts.append(part)
        
        return '\n'.join(content_parts)
    
    def _format_article(self, title, content, news_data):
        """格式化文章"""
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        article = f"""# {title}

**发布时间：** {timestamp}
**信息来源：** {news_data['source']}
**原文链接：** {news_data['link']}

---

{content}

---

*本文为AI辅助撰写的原创分析文章，仅供参考。*
"""
        return article
    
    def _extract_key_points(self, content):
        """提取关键点"""
        # 简单实现：提取包含数字或重要词汇的句子
        sentences = re.split(r'[。！？]', content)
        key_points = []
        
        important_patterns = [
            r'\d+%', r'\d+万', r'\d+亿', r'\d+年', r'\d+月',
            r'宣布', r'发布', r'启动', r'完成', r'增长', r'下降'
        ]
        
        for sentence in sentences:
            if any(re.search(pattern, sentence) for pattern in important_patterns):
                key_points.append(sentence.strip())
        
        return key_points[:5]
    
    def _extract_entities(self, content):
        """提取实体（简单实现）"""
        # 这里可以使用更复杂的NER模型
        import jieba.posseg as pseg
        
        entities = []
        words = pseg.cut(content)
        
        for word, flag in words:
            if flag in ['nr', 'ns', 'nt'] and len(word) > 1:  # 人名、地名、机构名
                entities.append(word)
        
        return list(set(entities))[:10]
    
    def _analyze_sentiment(self, content):
        """分析情感"""
        positive_words = ['好', '优秀', '成功', '增长', '提升', '改善', '突破', '创新']
        negative_words = ['坏', '失败', '下降', '问题', '危机', '困难', '风险', '担忧']
        
        import jieba
        words = list(jieba.cut(content))
        
        positive_count = sum(1 for word in words if word in positive_words)
        negative_count = sum(1 for word in words if word in negative_words)
        
        if positive_count > negative_count:
            return 'positive'
        elif negative_count > positive_count:
            return 'negative'
        else:
            return 'neutral'
    
    def _categorize_news(self, title):
        """新闻分类"""
        categories = {
            '经济': ['经济', '金融', '股市', '投资', 'GDP', '通胀', '贸易'],
            '科技': ['科技', '人工智能', '互联网', '5G', '芯片', '创新'],
            '政治': ['政府', '政策', '法律', '外交', '会议', '领导'],
            '社会': ['社会', '民生', '教育', '医疗', '环境', '文化'],
            '体育': ['体育', '奥运', '世界杯', '比赛', '运动员'],
            '娱乐': ['娱乐', '明星', '电影', '音乐', '综艺']
        }
        
        for category, keywords in categories.items():
            if any(keyword in title for keyword in keywords):
                return category
        
        return '综合'
    
    def _assess_urgency(self, news_data):
        """评估紧急程度"""
        urgent_keywords = ['突发', '紧急', '重大', '严重', '危机', '事故']
        
        if any(keyword in news_data['title'] for keyword in urgent_keywords):
            return 'high'
        elif (datetime.now() - news_data['publish_time']).total_seconds() < 3600:  # 1小时内
            return 'medium'
        else:
            return 'low'
    
    def _generate_fallback_article(self, news_data):
        """生成备用文章（当AI不可用时）"""
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        return f"""# 【独家报道】{news_data['title']}

**发布时间：** {timestamp}
**信息来源：** {news_data['source']}

## 事件概述

{news_data['summary']}

## 详细内容

{news_data['content'][:500]}...

## 分析观点

这一事件值得关注，我们将持续跟踪报道。

---

*本文基于公开信息整理，仅供参考。*
"""
